package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

type productUpdate struct {
	ID                string             `json:"id" validate:"required"`
	Name              string             `json:"name" validate:"required"`
	ImageURL          *string            `json:"image_url"`
	CommercialName    string             `json:"commercial_name" validate:"required"`
	Code              string             `json:"code" validate:"required"`
	SKUCode           string             `json:"sku_code" validate:"required"`
	MeasurementUnitID string             `json:"measurement_unit_id" validate:"required"`
	CategoryIDs       []string           `json:"category_ids" validate:"required,min=1"`
	BrandID           string             `json:"brand_id" validate:"required"`
	State             string             `json:"state"`
	Description       *string            `json:"description"`
	CanBeSold         bool               `json:"can_be_sold"`
	CanBePurchased    bool               `json:"can_be_purchased"`
	CostPrice         *float64           `json:"cost_price"`
	CostPriceTotal    int                `json:"cost_price_total"`
	ProductionInfo    *productionInfoDTO `json:"production_info"`
}

func productUpdateToModel(dto productUpdate) model.ProductUpdate {
	var productionInfo *model.ProductionInfo
	if dto.ProductionInfo != nil {
		materials := make([]model.Material, len(dto.ProductionInfo.Materials))
		for i, material := range dto.ProductionInfo.Materials {
			materials[i] = model.Material{
				ProductID: material.ProductID,
				Quantity:  material.Quantity,
			}
		}
		productionInfo = &model.ProductionInfo{
			ProductionType: dto.ProductionInfo.ProductionType,
			Materials:      materials,
		}
	}

	return model.ProductUpdate{
		ID:                dto.ID,
		Name:              dto.Name,
		ImageURL:          dto.ImageURL,
		CommercialName:    dto.CommercialName,
		Code:              dto.Code,
		SKUCode:           dto.SKUCode,
		MeasurementUnitID: dto.MeasurementUnitID,
		CategoryIDs:       dto.CategoryIDs,
		BrandID:           dto.BrandID,
		State:             dto.State,
		Description:       dto.Description,
		CanBeSold:         dto.CanBeSold,
		CanBePurchased:    dto.CanBePurchased,
		CostPrice:         dto.CostPrice,
		CostPriceTotal:    dto.CostPriceTotal,
		ProductionInfo:    productionInfo,
	}
}

// Update implements ProductHandler.
func (p *productHandler) Update(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[productUpdate](w, r, p.validator)
	if err != nil {
		utils.LogErr(ctx, p.log, err)
		return
	}

	if err := p.useCase.Update(ctx, productUpdateToModel(*req)); err != nil {
		utils.LogErr(ctx, p.log, err)
		respErrHandler(w, r, err, "Failed to update product")
		return
	}

	rest.SuccessResponse(w, r, http.StatusNoContent)
}
