package pg

import (
	"context"
	"fmt"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/oklog/ulid/v2"
)

func (p *productPostgreRepo) Create(ctx context.Context, product model.Product) error {
	return pg.ExecuteInSchema(ctx, p.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		// Start transaction
		tx, err := conn.Begin(ctx)
		if err != nil {
			return utils.InternalErrorf("failed to begin transaction", err, nil)
		}
		defer tx.Rollback(ctx)

		// Insert product
		query := `
			INSERT INTO products (
				id, name, image_url, commercial_name, code, sku_code,
				measurement_unit_id, brand_id, state,
				description, can_be_sold, can_be_purchased, cost_price,
				cost_price_total
			)
			VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
		`

		_, err = tx.Exec(ctx, query,
			product.ID,
			product.Name,
			product.ImageURL,
			product.CommercialName,
			product.Code,
			product.SKUCode,
			product.MeasurementUnitID,
			product.BrandID,
			product.State,
			product.Description,
			product.CanBeSold,
			product.CanBePurchased,
			product.CostPrice,
			product.CostPriceTotal,
		)

		if err != nil {
			return utils.InternalErrorf("failed to create product", err, nil)
		}

		fmt.Println(product.CategoryIDs)
		// Insert product-category relationships
		for _, categoryID := range product.CategoryIDs {
			relationID := ulid.Make().String()
			categoryQuery := `
				INSERT INTO product_categories (id, product_id, category_id)
				VALUES ($1, $2, $3)
			`
			_, err = tx.Exec(ctx, categoryQuery, relationID, product.ID, categoryID)
			if err != nil {
				return utils.InternalErrorf("failed to create product category relationship", err, nil)
			}
		}

		// Insert production info if provided
		if product.ProductionInfo != nil {
			productionInfoID := ulid.Make().String()
			productionInfoQuery := `
				INSERT INTO production_info (id, product_id, production_type)
				VALUES ($1, $2, $3)
			`
			_, err = tx.Exec(ctx, productionInfoQuery, productionInfoID, product.ID, product.ProductionInfo.ProductionType)
			if err != nil {
				return utils.InternalErrorf("failed to create production info", err, nil)
			}

			// Insert production info materials
			for _, material := range product.ProductionInfo.Materials {
				materialID := ulid.Make().String()
				materialQuery := `
					INSERT INTO production_info_materials (id, production_info_id, product_id, quantity)
					VALUES ($1, $2, $3, $4)
				`
				_, err = tx.Exec(ctx, materialQuery, materialID, productionInfoID, material.ProductID, material.Quantity)
				if err != nil {
					return utils.InternalErrorf("failed to create production info material", err, nil)
				}
			}
		}

		// Commit transaction
		err = tx.Commit(ctx)
		if err != nil {
			return utils.InternalErrorf("failed to commit transaction", err, nil)
		}

		return nil
	})
}
