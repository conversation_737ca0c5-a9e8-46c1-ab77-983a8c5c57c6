package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/oklog/ulid/v2"
)

func (p *productPostgreRepo) Update(ctx context.Context, product model.Product) error {
	return pg.ExecuteInSchema(ctx, p.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		// Start transaction
		tx, err := conn.Begin(ctx)
		if err != nil {
			return utils.InternalErrorf("failed to begin transaction", err, nil)
		}
		defer tx.Rollback(ctx)

		// Update product
		query := `
			UPDATE products SET
				name = $2,
				image_url = $3,
				commercial_name = $4,
				code = $5,
				sku_code = $6,
				measurement_unit_id = $7,
				brand_id = $8,
				state = $9,
				description = $10,
				can_be_sold = $11,
				can_be_purchased = $12,
				cost_price = $13,
				cost_price_total = $14,
				updated_at = CURRENT_TIMESTAMP
			WHERE id = $1 AND deleted_at IS NULL
		`

		result, err := tx.Exec(ctx, query,
			product.ID,
			product.Name,
			product.ImageURL,
			product.CommercialName,
			product.Code,
			product.SKUCode,
			product.MeasurementUnitID,
			product.BrandID,
			product.State,
			product.Description,
			product.CanBeSold,
			product.CanBePurchased,
			product.CostPrice,
			product.CostPriceTotal,
		)

		if err != nil {
			return utils.InternalErrorf("failed to update product", err, nil)
		}

		if result.RowsAffected() == 0 {
			return model.ProductNotFoundf("Product not found", nil, nil)
		}

		// Delete existing product-category relationships
		deleteQuery := `DELETE FROM product_categories WHERE product_id = $1`
		_, err = tx.Exec(ctx, deleteQuery, product.ID)
		if err != nil {
			return utils.InternalErrorf("failed to delete existing product categories", err, nil)
		}

		// Insert new product-category relationships
		for _, categoryID := range product.CategoryIDs {
			relationID := ulid.Make().String()
			categoryQuery := `
				INSERT INTO product_categories (id, product_id, category_id)
				VALUES ($1, $2, $3)
			`
			_, err = tx.Exec(ctx, categoryQuery, relationID, product.ID, categoryID)
			if err != nil {
				return utils.InternalErrorf("failed to create product category relationship", err, nil)
			}
		}

		// Handle production info update
		// First, delete existing production info and materials
		deleteProductionInfoMaterialsQuery := `
			DELETE FROM production_info_materials
			WHERE production_info_id IN (
				SELECT id FROM production_info WHERE product_id = $1
			)
		`
		_, err = tx.Exec(ctx, deleteProductionInfoMaterialsQuery, product.ID)
		if err != nil {
			return utils.InternalErrorf("failed to delete existing production info materials", err, nil)
		}

		deleteProductionInfoQuery := `DELETE FROM production_info WHERE product_id = $1`
		_, err = tx.Exec(ctx, deleteProductionInfoQuery, product.ID)
		if err != nil {
			return utils.InternalErrorf("failed to delete existing production info", err, nil)
		}

		// Insert new production info if provided
		if product.ProductionInfo != nil {
			productionInfoID := ulid.Make().String()
			productionInfoQuery := `
				INSERT INTO production_info (id, product_id, production_type)
				VALUES ($1, $2, $3)
			`
			_, err = tx.Exec(ctx, productionInfoQuery, productionInfoID, product.ID, product.ProductionInfo.ProductionType)
			if err != nil {
				return utils.InternalErrorf("failed to create production info", err, nil)
			}

			// Insert production info materials
			for _, material := range product.ProductionInfo.Materials {
				materialID := ulid.Make().String()
				materialQuery := `
					INSERT INTO production_info_materials (id, production_info_id, product_id, quantity)
					VALUES ($1, $2, $3, $4)
				`
				_, err = tx.Exec(ctx, materialQuery, materialID, productionInfoID, material.ProductID, material.Quantity)
				if err != nil {
					return utils.InternalErrorf("failed to create production info material", err, nil)
				}
			}
		}

		// Commit transaction
		err = tx.Commit(ctx)
		if err != nil {
			return utils.InternalErrorf("failed to commit transaction", err, nil)
		}

		return nil
	})
}
